import logging
import json
import uuid
from typing import Dict, List, Optional, Any
import requests
from fastapi import HTTPException
from pydantic import BaseModel
from sqlmodel import Session
import time

from app.core.config import settings
from app.models import (
    UsageTypeEnum, ImageGenerationRecord, ImageGenerationRecordCreate
)
from app.services.billing import BillingService
from app.services.trial_service import TrialService

logger = logging.getLogger(__name__)


# 彩色日志工具类
class ColoredLogger:
    """彩色日志工具类，用于美化API调用日志"""

    # ANSI颜色代码
    COLORS = {
        'RESET': '\033[0m',
        'BOLD': '\033[1m',
        'RED': '\033[91m',
        'GREEN': '\033[92m',
        'YELLOW': '\033[93m',
        'BLUE': '\033[94m',
        'MAGENTA': '\033[95m',
        'CYAN': '\033[96m',
        'WHITE': '\033[97m',
        'GRAY': '\033[90m',
    }

    @classmethod
    def _colorize(cls, text: str, color: str) -> str:
        """给文本添加颜色"""
        return f"{cls.COLORS.get(color, '')}{text}{cls.COLORS['RESET']}"

    @classmethod
    def log_api_request(cls, method: str, url: str, headers: Dict, data: Dict, request_id: str = None):
        """记录API请求信息"""
        request_id = request_id or str(uuid.uuid4())[:8]

        logger.info(
            f"\n{cls._colorize('🚀 API REQUEST', 'CYAN')} "
            f"{cls._colorize(f'[{request_id}]', 'GRAY')}\n"
            f"{cls._colorize('Method:', 'BLUE')} {cls._colorize(method, 'BOLD')}\n"
            f"{cls._colorize('URL:', 'BLUE')} {cls._colorize(url, 'WHITE')}\n"
            f"{cls._colorize('Headers:', 'BLUE')} {cls._colorize(json.dumps(headers, indent=2, ensure_ascii=False), 'GRAY')}\n"
            f"{cls._colorize('Data:', 'BLUE')} {cls._colorize(json.dumps(data, indent=2, ensure_ascii=False), 'YELLOW')}"
        )
        return request_id

    @classmethod
    def log_api_response(cls, status_code: int, response_data: Dict, duration: float, request_id: str = None):
        """记录API响应信息"""
        request_id = request_id or "unknown"

        # 根据状态码选择颜色
        if 200 <= status_code < 300:
            status_color = 'GREEN'
            icon = '✅'
        elif 400 <= status_code < 500:
            status_color = 'YELLOW'
            icon = '⚠️'
        else:
            status_color = 'RED'
            icon = '❌'

        logger.info(
            f"\n{cls._colorize(f'{icon} API RESPONSE', 'CYAN')} "
            f"{cls._colorize(f'[{request_id}]', 'GRAY')}\n"
            f"{cls._colorize('Status:', 'BLUE')} {cls._colorize(str(status_code), status_color)}\n"
            f"{cls._colorize('Duration:', 'BLUE')} {cls._colorize(f'{duration:.3f}s', 'MAGENTA')}\n"
            f"{cls._colorize('Response:', 'BLUE')} {cls._colorize(json.dumps(response_data, indent=2, ensure_ascii=False), 'WHITE')}"
        )

    @classmethod
    def log_api_error(cls, error: Exception, duration: float, request_id: str = None):
        """记录API错误信息"""
        request_id = request_id or "unknown"

        logger.error(
            f"\n{cls._colorize('💥 API ERROR', 'RED')} "
            f"{cls._colorize(f'[{request_id}]', 'GRAY')}\n"
            f"{cls._colorize('Duration:', 'BLUE')} {cls._colorize(f'{duration:.3f}s', 'MAGENTA')}\n"
            f"{cls._colorize('Error:', 'BLUE')} {cls._colorize(str(error), 'RED')}"
        )

    @classmethod
    def log_service_info(cls, message: str, data: Dict = None):
        """记录服务信息"""
        log_msg = f"{cls._colorize('ℹ️  SERVICE INFO', 'BLUE')} {cls._colorize(message, 'WHITE')}"
        if data:
            log_msg += f"\n{cls._colorize('Data:', 'BLUE')} {cls._colorize(json.dumps(data, indent=2, ensure_ascii=False), 'GRAY')}"
        logger.info(log_msg)

    @classmethod
    def log_service_warning(cls, message: str, data: Dict = None):
        """记录服务警告"""
        log_msg = f"{cls._colorize('⚠️  SERVICE WARNING', 'YELLOW')} {cls._colorize(message, 'YELLOW')}"
        if data:
            log_msg += f"\n{cls._colorize('Data:', 'BLUE')} {cls._colorize(json.dumps(data, indent=2, ensure_ascii=False), 'GRAY')}"
        logger.warning(log_msg)

    @classmethod
    def log_service_error(cls, message: str, error: Exception = None, data: Dict = None):
        """记录服务错误"""
        log_msg = f"{cls._colorize('🔥 SERVICE ERROR', 'RED')} {cls._colorize(message, 'RED')}"
        if error:
            log_msg += f"\n{cls._colorize('Exception:', 'BLUE')} {cls._colorize(str(error), 'RED')}"
        if data:
            log_msg += f"\n{cls._colorize('Data:', 'BLUE')} {cls._colorize(json.dumps(data, indent=2, ensure_ascii=False), 'GRAY')}"
        logger.error(log_msg)


# 第三方API状态码映射
THIRD_PARTY_API_STATUS_MAPPING = {
    # 第三方API状态码 -> (HTTP状态码, 错误消息)
    200: (200, "请求已成功处理"),
    400: (400, "参数不是有效的 JSON 格式"),
    401: (401, "缺少身份验证凭据或凭据无效"),
    402: (402, "账户没有足够的积分执行此操作"),
    404: (404, "请求的资源或端点不存在"),
    422: (422, "请求参数未通过验证检查"),
    429: (429, "已超过对此资源的请求限制"),
    455: (503, "系统当前正在进行维护"),
    500: (500, "在处理请求时发生意外错误"),
    550: (503, "任务因队列已满而被拒绝，可能是由于源站点问题导致。请联系管理员确认"),
}

# 第三方API错误消息映射（中文）
THIRD_PARTY_ERROR_MESSAGES = {
    400: "图片生成请求格式错误，请检查参数格式",
    401: "图片生成服务认证失败，请稍后重试",
    402: "图片生成积分不足，请充值后重试",
    404: "图片生成服务端点不存在",
    422: "图片生成参数验证失败，请检查输入参数",
    429: "图片生成请求过于频繁，请稍后重试",
    455: "图片生成服务正在维护中，请稍后重试",
    500: "图片生成服务内部错误，请稍后重试",
    550: "图片生成服务队列已满，请稍后重试",
}


def get_third_party_error_info(api_status_code: int) -> tuple[int, str]:
    """
    根据第三方API状态码获取对应的HTTP状态码和错误消息

    Args:
        api_status_code: 第三方API返回的状态码

    Returns:
        tuple: (HTTP状态码, 错误消息)
    """
    if api_status_code in THIRD_PARTY_API_STATUS_MAPPING:
        http_status, _ = THIRD_PARTY_API_STATUS_MAPPING[api_status_code]
        message = THIRD_PARTY_ERROR_MESSAGES.get(api_status_code, "图片生成服务返回未知错误")
        return http_status, message
    else:
        # 未知状态码，默认为500
        return 500, f"图片生成服务返回未知状态码: {api_status_code}"


class ImageGenerationRequest(BaseModel):
    """图片生成请求模型"""
    files_url: Optional[List[str]] = None
    filesUrl: Optional[List[str]] = None  # 🆕 支持驼峰命名格式
    prompt: str
    size: str = "1:1"  # 支持的尺寸: "1:1", "16:9", "9:16", "4:3", "3:4"
    callback_url: Optional[str] = None
    callBackUrl: Optional[str] = None  # 🆕 支持驼峰命名格式
    is_enhance: bool = False
    isEnhance: Optional[bool] = None  # 🆕 支持驼峰命名格式
    upload_cn: bool = False
    uploadCn: Optional[bool] = None  # 🆕 支持驼峰命名格式
    n_variants: int = 1
    nVariants: Optional[int] = None  # 🆕 支持驼峰命名格式
    enable_fallback: bool = False
    enableFallback: Optional[bool] = None  # 🆕 支持驼峰命名格式
    fallback_model: str = "FLUX_MAX"
    fallbackModel: Optional[str] = None  # 🆕 支持驼峰命名格式

    def get_files_url(self) -> Optional[List[str]]:
        """获取图片URL列表，支持两种命名格式"""
        # 优先使用 filesUrl（驼峰格式），如果没有则使用 files_url（下划线格式）
        return self.filesUrl or self.files_url

    def get_callback_url(self) -> Optional[str]:
        """获取回调URL，支持两种命名格式"""
        return self.callBackUrl or self.callback_url

    def get_is_enhance(self) -> bool:
        """获取是否增强，支持两种命名格式"""
        return self.isEnhance if self.isEnhance is not None else self.is_enhance

    def get_upload_cn(self) -> bool:
        """获取是否上传到中国，支持两种命名格式"""
        return self.uploadCn if self.uploadCn is not None else self.upload_cn

    def get_n_variants(self) -> int:
        """获取变体数量，支持两种命名格式"""
        return self.nVariants if self.nVariants is not None else self.n_variants

    def get_enable_fallback(self) -> bool:
        """获取是否启用回退，支持两种命名格式"""
        return self.enableFallback if self.enableFallback is not None else self.enable_fallback

    def get_fallback_model(self) -> str:
        """获取回退模型，支持两种命名格式"""
        return self.fallbackModel or self.fallback_model


class ImageGenerationResponse(BaseModel):
    """图片生成响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    error_code: Optional[str] = None  # 🆕 添加错误代码
    http_status_code: Optional[int] = None  # 🆕 添加HTTP状态码


class ImageGenerationRecordInfoResponse(BaseModel):
    """图片生成记录详情响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    error_code: Optional[str] = None
    http_status_code: Optional[int] = None


class ImageGenerationService:
    """图片生成服务类"""

    def __init__(self, session: Optional[Session] = None):
        self.api_url = settings.IMAGE_GENERATION_API_URL
        self.api_token = settings.IMAGE_GENERATION_API_TOKEN
        self.session = session

        # 设置记录详情API的URL
        self.record_info_api_url = "https://api.kie.ai/api/v1/gpt4o-image/record-info"

        if not self.api_token:
            logger.warning("IMAGE_GENERATION_API_TOKEN is not configured")

    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }

    def _prepare_payload(self, request: ImageGenerationRequest) -> Dict[str, Any]:
        """准备请求载荷"""
        # 🔧 修改：使用getter方法支持两种命名格式
        payload = {
            "prompt": request.prompt,
            "size": request.size,
            "isEnhance": request.get_is_enhance(),
            "uploadCn": request.get_upload_cn(),
            "nVariants": request.get_n_variants(),
            "enableFallback": request.get_enable_fallback(),
            "fallbackModel": request.get_fallback_model()
        }

        # 🔧 修改：使用新的方法获取图片URL列表，支持两种命名格式
        files_url_list = request.get_files_url()
        if files_url_list:
            payload["filesUrl"] = files_url_list
            logger.debug(f"Adding {len(files_url_list)} input images to API payload")

        # 🔧 修改：优先使用请求中的回调URL，如果没有则使用配置文件中的
        callback_url = request.get_callback_url()
        if callback_url:
            payload["callBackUrl"] = callback_url
            logger.debug(f"Using request callback URL: {callback_url}")
        else:
            from app.core.config import settings
            if settings.IMAGE_GENERATION_CALLBACK_URL:
                payload["callBackUrl"] = settings.IMAGE_GENERATION_CALLBACK_URL
                logger.debug(f"Using config callback URL: {settings.IMAGE_GENERATION_CALLBACK_URL}")

        return payload

    def _check_user_access(self, user_id: uuid.UUID) -> tuple[bool, str, Dict, Optional[str]]:
        """
        检查用户是否有权限使用图片生成服务

        Returns:
            tuple: (success, message, data, error_code)
        """
        if not self.session:
            logger.error(f"Database session not available for user {user_id}")
            return False, "Database session not available", {}, "DATABASE_ERROR"

        billing_service = BillingService(self.session)

        logger.info(f"Checking access for user {user_id}, usage_type: image_generation")

        # 检查用户权限
        access_check = billing_service.check_access(user_id, UsageTypeEnum.image_generation)

        logger.info(f"Access check result for user {user_id}: can_access={access_check.can_access}, "
                   f"remaining_credits={access_check.remaining_credits}, "
                   f"remaining_monthly_usage={access_check.remaining_monthly_usage}, "
                   f"subscription_status={access_check.subscription_status}, "
                   f"reason='{access_check.reason}'")

        if not access_check.can_access:
            # 根据具体原因返回不同的错误代码
            reason = access_check.reason or "Access denied"
            error_code = None

            if "no active subscription" in reason.lower() and "credits available" in reason.lower():
                error_code = "NO_SUBSCRIPTION_OR_CREDITS"
            elif "monthly usage limit" in reason.lower():
                error_code = "MONTHLY_LIMIT_REACHED"
            elif "insufficient credits" in reason.lower():
                error_code = "INSUFFICIENT_CREDITS"
            else:
                error_code = "PERMISSION_DENIED"

            logger.warning(f"Access denied for user {user_id}: {error_code} - {reason}")

            return False, reason, {
                "remaining_credits": access_check.remaining_credits,
                "remaining_monthly_usage": access_check.remaining_monthly_usage,
                "subscription_status": access_check.subscription_status
            }, error_code

        logger.info(f"Access granted for user {user_id}")
        return True, "Access granted", {
            "remaining_credits": access_check.remaining_credits,
            "remaining_monthly_usage": access_check.remaining_monthly_usage,
            "subscription_status": access_check.subscription_status
        }, None

    def _consume_user_usage(self, user_id: uuid.UUID) -> tuple[bool, str, Dict, Optional[str]]:
        """
        消耗用户的使用配额或积分

        Returns:
            tuple: (success, message, details, error_code)
        """
        if not self.session:
            logger.error(f"Database session not available for user {user_id}")
            return False, "Database session not available", {}, "DATABASE_ERROR"

        billing_service = BillingService(self.session)

        logger.info(f"Attempting to consume usage for user {user_id}, type: image_generation, count: 1")

        # 消耗使用配额 - 注意这里只返回3个值
        success, message, details = billing_service.consume_usage(
            user_id, UsageTypeEnum.image_generation, 1
        )

        logger.info(f"Usage consumption result for user {user_id}: success={success}, message='{message}', details={details}")

        # 根据失败原因确定错误代码
        error_code = None
        if not success:
            if "insufficient credits" in message.lower():
                error_code = "INSUFFICIENT_CREDITS"
            elif "monthly usage limit" in message.lower():
                error_code = "MONTHLY_LIMIT_REACHED"
            elif "no available usage quota" in message.lower():
                error_code = "NO_SUBSCRIPTION_OR_CREDITS"
            else:
                error_code = "PERMISSION_DENIED"

            logger.warning(f"Usage consumption failed for user {user_id}: {error_code} - {message}")
        else:
            logger.info(f"Usage consumption successful for user {user_id}")

        return success, message, details, error_code

    def _create_generation_record(
        self,
        user_id: uuid.UUID,
        request: ImageGenerationRequest,
        status: str = "pending",
        api_response: Optional[Dict] = None,
        error_message: Optional[str] = None
    ) -> Optional[ImageGenerationRecord]:
        """创建图片生成记录"""
        if not self.session:
            return None

        try:
            # 🔧 修改：使用新的方法获取图片URL列表，支持两种命名格式
            files_url_list = request.get_files_url()
            files_url_str = None
            if files_url_list:
                files_url_str = json.dumps(files_url_list, ensure_ascii=False)
                logger.info(f"Saving {len(files_url_list)} input image URLs to database")
                logger.debug(f"Input image URLs: {files_url_list}")

            # 准备API响应字符串
            api_response_str = None
            if api_response:
                api_response_str = json.dumps(api_response, ensure_ascii=False)

            # 🔧 修改：使用getter方法获取回调URL，支持两种命名格式
            callback_url = request.get_callback_url()

            # 创建记录
            record_data = ImageGenerationRecordCreate(
                user_id=user_id,
                prompt=request.prompt,
                size=request.size,
                files_url=files_url_str,  # 🎯 保存输入的图片URL列表
                callback_url=callback_url,  # 🔧 使用getter方法获取的回调URL
                is_enhance=request.get_is_enhance(),  # 🔧 使用getter方法
                n_variants=request.get_n_variants(),  # 🔧 使用getter方法
                api_response=api_response_str,
                status=status,
                error_message=error_message
            )

            record = ImageGenerationRecord.model_validate(record_data)
            self.session.add(record)
            self.session.commit()
            self.session.refresh(record)

            # 🎨 彩色日志：记录创建成功
            logger.info(f"✅ Generation record created: {record.id}")
            if files_url_list:
                logger.info(f"📷 Input images saved: {len(files_url_list)} URLs")

            return record

        except Exception as e:
            logger.error(f"Failed to create generation record: {str(e)}")
            return None

    def _update_generation_record(
        self,
        record: ImageGenerationRecord,
        status: str,
        api_response: Optional[Dict] = None,
        error_message: Optional[str] = None,
        task_id: Optional[str] = None
    ) -> bool:
        """更新图片生成记录"""
        if not self.session:
            return False

        try:
            record.status = status
            if api_response:
                record.api_response = json.dumps(api_response)
            if error_message:
                record.error_message = error_message
            if task_id:
                record.task_id = task_id
                logger.info(f"Updated record {record.id} with task_id: {task_id}")

            self.session.add(record)
            self.session.commit()
            return True

        except Exception as e:
            logger.error(f"Failed to update generation record: {str(e)}")
            return False

    def _link_usage_record_to_generation(self, user_id: uuid.UUID, generation_record_id: uuid.UUID) -> bool:
        """
        关联UsageRecord到ImageGenerationRecord

        Args:
            user_id: 用户ID
            generation_record_id: 生成记录ID

        Returns:
            bool: 是否成功关联
        """
        if not self.session:
            return False

        try:
            from sqlmodel import select
            from app.models import UsageRecord, UsageTypeEnum

            # 查找最近的图片生成使用记录
            statement = select(UsageRecord).where(
                UsageRecord.user_id == user_id,
                UsageRecord.usage_type == UsageTypeEnum.image_generation,
                UsageRecord.related_record_id.is_(None)  # 未关联的记录
            ).order_by(UsageRecord.used_at.desc()).limit(1)

            usage_record = self.session.exec(statement).first()

            if usage_record:
                usage_record.related_record_id = generation_record_id
                usage_record.related_record_type = "image_generation"
                self.session.add(usage_record)
                self.session.commit()
                logger.info(f"Linked usage record {usage_record.id} to generation record {generation_record_id}")
                return True
            else:
                logger.warning(f"No unlinked usage record found for user {user_id}")
                return False

        except Exception as e:
            logger.error(f"Failed to link usage record to generation record: {str(e)}")
            return False

    async def generate_image_with_user_check(
        self,
        user_id: uuid.UUID,
        request: ImageGenerationRequest
    ) -> ImageGenerationResponse:
        """
        带用户权限检查的图片生成

        Args:
            user_id: 用户ID
            request: 图片生成请求

        Returns:
            ImageGenerationResponse: 生成结果
        """
        logger.info(f"Starting image generation for user {user_id}, prompt: '{request.prompt[:50]}...', size: {request.size}")

        # 1. 检查用户权限
        can_access, access_message, access_details, access_error_code = self._check_user_access(user_id)
        if not can_access:
            logger.error(f"Access check failed for user {user_id}: {access_error_code} - {access_message}")

            # 创建失败记录
            self._create_generation_record(
                user_id, request, status="failed", error_message=access_message
            )

            # 🆕 返回标准化错误响应
            http_status_code = 403  # 权限相关错误默认403
            if access_error_code == "DATABASE_ERROR":
                http_status_code = 500

            return ImageGenerationResponse(
                success=False,
                message=access_message,
                error=access_message,
                error_code=access_error_code,
                http_status_code=http_status_code,
                data=access_details
            )

        # 2. 消耗用户配额
        usage_success, usage_message, usage_details, usage_error_code = self._consume_user_usage(user_id)
        if not usage_success:
            logger.error(f"Usage consumption failed for user {user_id}: {usage_error_code} - {usage_message}")

            # 创建失败记录
            self._create_generation_record(
                user_id, request, status="failed", error_message=usage_message
            )

            # 🆕 返回标准化错误响应
            http_status_code = 403  # 配额相关错误默认403
            if usage_error_code == "DATABASE_ERROR":
                http_status_code = 500

            return ImageGenerationResponse(
                success=False,
                message=usage_message,
                error=usage_message,
                error_code=usage_error_code,
                http_status_code=http_status_code,
                data=usage_details
            )

        # 3. 创建待处理记录
        generation_record = self._create_generation_record(user_id, request, status="pending")

        # 4. 调用API生成图片
        logger.info(f"Calling image generation API for user {user_id}")
        logger.debug(f"API request payload: prompt='{request.prompt}', size={request.size}, "
                    f"n_variants={request.n_variants}, is_enhance={request.is_enhance}, "
                    f"files_url={request.files_url}")

        api_result = await self.generate_image(request)

        logger.info(f"API call completed for user {user_id}: success={api_result.success}")
        if not api_result.success:
            logger.error(f"API call failed for user {user_id}: {api_result.error}")
        else:
            logger.debug(f"API response data for user {user_id}: {api_result.data}")

        # 5. 更新记录状态
        if generation_record:
            if api_result.success:
                self._update_generation_record(
                    generation_record,
                    status="success",
                    api_response=api_result.data
                )
            else:
                self._update_generation_record(
                    generation_record,
                    status="failed",
                    error_message=api_result.error
                )

        # 6. 返回结果，包含使用情况和试用状态提示
        if api_result.success:
            # 合并使用详情到响应数据
            response_data = api_result.data or {}
            response_data.update(usage_details)
            response_data["generation_record_id"] = str(generation_record.id) if generation_record else None

            # 🆕 添加试用状态提示
            try:
                trial_service = TrialService(self.session)
                if trial_service.is_trial_user(user_id):
                    trial_message = trial_service.get_trial_usage_message(user_id)
                    response_data["trial_message"] = trial_message
            except Exception as e:
                logger.error(f"Failed to get trial message for user {user_id}: {str(e)}")

            return ImageGenerationResponse(
                success=True,
                message=f"Image generation successful. {usage_message}",
                data=response_data
            )
        else:
            return api_result

    async def generate_image(self, request: ImageGenerationRequest) -> ImageGenerationResponse:
        """
        生成图片

        Args:
            request: 图片生成请求

        Returns:
            ImageGenerationResponse: 生成结果
        """
        try:
            if not self.api_token:
                return ImageGenerationResponse(
                    success=False,
                    message="API token not configured",
                    error="IMAGE_GENERATION_API_TOKEN is not set in environment variables"
                )

            payload = self._prepare_payload(request)
            headers = self._get_headers()

            logger.info(f"Sending image generation request to {self.api_url}")
            logger.debug(f"Request payload: {payload}")

            response = requests.post(
                self.api_url,
                json=payload,
                headers=headers,
                timeout=30
            )

            response_data = response.json()

            # 检查API响应中的错误
            if response.status_code != 200:
                error_msg = f"API returned status {response.status_code}"
                if 'msg' in response_data:
                    error_msg += f": {response_data['msg']}"
                logger.error(error_msg)
                return ImageGenerationResponse(
                    success=False,
                    message="API request failed",
                    error=error_msg,
                    data=response_data
                )

            # 检查响应数据中的错误码
            if 'code' in response_data and response_data['code'] != 200:
                api_status_code = response_data['code']
                api_message = response_data.get('msg', '')

                # 使用第三方API状态码映射
                http_status_code, error_message = get_third_party_error_info(api_status_code)

                # 如果API返回了具体的错误消息，优先使用
                if api_message:
                    error_message = f"{error_message}: {api_message}"

                logger.error(f"API error code {api_status_code}: {error_message}")

                return ImageGenerationResponse(
                    success=False,
                    message=error_message,
                    error=f"API error code {api_status_code}: {api_message}" if api_message else f"API error code {api_status_code}",
                    error_code=f"API_ERROR_{api_status_code}",
                    http_status_code=http_status_code,
                    data=response_data
                )

            logger.info("Image generation request successful")
            logger.debug(f"Response data: {response_data}")

            return ImageGenerationResponse(
                success=True,
                message="Image generation request submitted successfully",
                data=response_data
            )

        except requests.exceptions.RequestException as e:
            error_msg = f"Request failed: {str(e)}"
            logger.error(error_msg)
            return ImageGenerationResponse(
                success=False,
                message="Failed to generate image",
                error=error_msg
            )
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(error_msg)
            return ImageGenerationResponse(
                success=False,
                message="An unexpected error occurred",
                error=error_msg
            )

    def generate_image_sync(self, request: ImageGenerationRequest) -> ImageGenerationResponse:
        """
        同步生成图片（用于测试或简单场景）

        Args:
            request: 图片生成请求

        Returns:
            ImageGenerationResponse: 生成结果
        """
        try:
            if not self.api_token:
                return ImageGenerationResponse(
                    success=False,
                    message="API token not configured",
                    error="IMAGE_GENERATION_API_TOKEN is not set in environment variables"
                )

            payload = self._prepare_payload(request)
            headers = self._get_headers()

            # 🎨 彩色日志：记录API请求
            request_id = ColoredLogger.log_api_request(
                method="POST",
                url=self.api_url,
                headers=headers,
                data=payload
            )

            start_time = time.time()

            response = requests.post(
                self.api_url,
                json=payload,
                headers=headers,
                timeout=30
            )

            duration = time.time() - start_time
            response_data = response.json()

            # 🎨 彩色日志：记录API响应
            ColoredLogger.log_api_response(
                status_code=response.status_code,
                response_data=response_data,
                duration=duration,
                request_id=request_id
            )

            # 检查API响应中的错误
            if response.status_code != 200:
                error_msg = f"API returned status {response.status_code}"
                if 'msg' in response_data:
                    error_msg += f": {response_data['msg']}"
                ColoredLogger.log_service_error(error_msg, data=response_data)
                return ImageGenerationResponse(
                    success=False,
                    message="API request failed",
                    error=error_msg,
                    data=response_data
                )

            # 检查响应数据中的错误码
            if 'code' in response_data and response_data['code'] != 200:
                api_status_code = response_data['code']
                api_message = response_data.get('msg', '')

                # 使用第三方API状态码映射
                http_status_code, error_message = get_third_party_error_info(api_status_code)

                # 如果API返回了具体的错误消息，优先使用
                if api_message:
                    error_message = f"{error_message}: {api_message}"

                logger.error(f"API error code {api_status_code}: {error_message}")

                return ImageGenerationResponse(
                    success=False,
                    message=error_message,
                    error=f"API error code {api_status_code}: {api_message}" if api_message else f"API error code {api_status_code}",
                    error_code=f"API_ERROR_{api_status_code}",
                    http_status_code=http_status_code,
                    data=response_data
                )

            logger.info("Image generation request successful")
            logger.debug(f"Response data: {response_data}")

            return ImageGenerationResponse(
                success=True,
                message="Image generation request submitted successfully",
                data=response_data
            )

        except requests.exceptions.RequestException as e:
            duration = time.time() - start_time
            # 🎨 彩色日志：记录API错误
            ColoredLogger.log_api_error(e, duration, request_id)

            error_msg = f"Request failed: {str(e)}"
            return ImageGenerationResponse(
                success=False,
                message="Failed to generate image",
                error=error_msg
            )
        except Exception as e:
            duration = time.time() - start_time
            # 🎨 彩色日志：记录服务错误
            ColoredLogger.log_service_error(f"Unexpected error in generate_image", e)
            return ImageGenerationResponse(
                success=False,
                message="An unexpected error occurred",
                error=error_msg
            )

    def generate_image_sync_with_user_check(
        self,
        user_id: uuid.UUID,
        request: ImageGenerationRequest
    ) -> ImageGenerationResponse:
        """
        带用户权限检查的同步图片生成

        Args:
            user_id: 用户ID
            request: 图片生成请求

        Returns:
            ImageGenerationResponse: 生成结果
        """
        # 🎨 彩色日志：服务开始
        ColoredLogger.log_service_info(
            f"Starting sync image generation for user {user_id}",
            {
                "prompt": request.prompt[:50] + "..." if len(request.prompt) > 50 else request.prompt,
                "size": request.size,
                "n_variants": request.n_variants,
                "is_enhance": request.is_enhance,
                "has_files": bool(request.files_url)
            }
        )

        # 1. 检查用户权限
        can_access, access_message, access_details, access_error_code = self._check_user_access(user_id)
        if not can_access:
            # 🎨 彩色日志：权限检查失败
            ColoredLogger.log_service_error(
                f"Access check failed for user {user_id}: {access_error_code}",
                data={
                    "error_code": access_error_code,
                    "message": access_message,
                    "details": access_details
                }
            )

            # 创建失败记录
            self._create_generation_record(
                user_id, request, status="failed", error_message=access_message
            )

            http_status_code = 403  # 权限相关错误默认403
            if access_error_code == "DATABASE_ERROR":
                http_status_code = 500

            return ImageGenerationResponse(
                success=False,
                message=access_message,
                error=access_message,
                error_code=access_error_code,
                http_status_code=http_status_code,
                data=access_details
            )

        # 🎨 彩色日志：权限检查成功
        ColoredLogger.log_service_info(
            f"Access check passed for user {user_id}",
            access_details
        )

        # 2. 消耗用户配额
        usage_success, usage_message, usage_details, usage_error_code = self._consume_user_usage(user_id)
        if not usage_success:
            # 🎨 彩色日志：配额消耗失败
            ColoredLogger.log_service_error(
                f"Usage consumption failed for user {user_id}: {usage_error_code}",
                data={
                    "error_code": usage_error_code,
                    "message": usage_message,
                    "details": usage_details
                }
            )

            # 创建失败记录
            self._create_generation_record(
                user_id, request, status="failed", error_message=usage_message
            )

            http_status_code = 403  # 配额相关错误默认403
            if usage_error_code == "DATABASE_ERROR":
                http_status_code = 500

            return ImageGenerationResponse(
                success=False,
                message=usage_message,
                error=usage_message,
                error_code=usage_error_code,
                http_status_code=http_status_code,
                data=usage_details
            )

        # 🎨 彩色日志：配额消耗成功
        ColoredLogger.log_service_info(
            f"Usage consumption successful for user {user_id}",
            usage_details
        )

        # 3. 创建待处理记录
        generation_record = self._create_generation_record(user_id, request, status="pending")

        # 🎨 彩色日志：记录创建
        ColoredLogger.log_service_info(
            f"Generation record created for user {user_id}",
            {
                "record_id": str(generation_record.id) if generation_record else None,
                "status": "pending"
            }
        )

        # 4. 调用API生成图片
        ColoredLogger.log_service_info(
            f"Calling sync image generation API for user {user_id}",
            {
                "prompt": request.prompt[:50] + "..." if len(request.prompt) > 50 else request.prompt,
                "size": request.size,
                "n_variants": request.n_variants,
                "is_enhance": request.is_enhance,
                "has_files": bool(request.files_url)
            }
        )

        api_result = self.generate_image_sync(request)

        # 🎨 彩色日志：API调用结果
        if api_result.success:
            ColoredLogger.log_service_info(
                f"Sync API call successful for user {user_id}",
                {"has_data": bool(api_result.data)}
            )
        else:
            ColoredLogger.log_service_error(
                f"Sync API call failed for user {user_id}",
                data={"error": api_result.error}
            )

        # 5. 更新记录状态和taskId
        if generation_record:
            if api_result.success:
                # 🔧 修复：正确提取taskId
                task_id = None
                if api_result.data and isinstance(api_result.data, dict):
                    # 尝试多种可能的结构
                    if "data" in api_result.data and isinstance(api_result.data["data"], dict):
                        # 结构: {"code": 200, "data": {"taskId": "xxx"}}
                        task_id = api_result.data["data"].get("taskId")
                    else:
                        # 结构: {"taskId": "xxx"} 或其他
                        task_id = api_result.data.get("taskId")

                # 🎨 彩色日志：记录task_id提取结果
                if task_id:
                    ColoredLogger.log_service_info(
                        f"Extracted task_id from API response: {task_id}",
                        {"api_response_structure": type(api_result.data).__name__}
                    )
                else:
                    ColoredLogger.log_service_warning(
                        "Failed to extract task_id from API response",
                        {"api_response": api_result.data}
                    )

                self._update_generation_record(
                    generation_record,
                    status="success",
                    api_response=api_result.data,
                    task_id=task_id
                )

                # 🆕 关联UsageRecord
                self._link_usage_record_to_generation(user_id, generation_record.id)
            else:
                self._update_generation_record(
                    generation_record,
                    status="failed",
                    error_message=api_result.error
                )

        # 6. 返回结果，包含使用情况和试用状态提示
        if api_result.success:
            # 合并使用详情到响应数据
            response_data = api_result.data or {}
            response_data.update(usage_details)
            response_data["generation_record_id"] = str(generation_record.id) if generation_record else None

            # 🆕 添加试用状态提示
            try:
                trial_service = TrialService(self.session)
                if trial_service.is_trial_user(user_id):
                    trial_message = trial_service.get_trial_usage_message(user_id)
                    response_data["trial_message"] = trial_message
            except Exception as e:
                logger.error(f"Failed to get trial message for user {user_id}: {str(e)}")

            return ImageGenerationResponse(
                success=True,
                message=f"Image generation successful. {usage_message}",
                data=response_data
            )
        else:
            return api_result

    def get_generation_record_info(self, task_id: str) -> Dict[str, Any]:
        """
        获取图片生成记录详情 - 直接返回API调用结果

        Args:
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 直接返回第三方API的响应数据，不进行包装
        """
        try:
            if not self.api_token:
                # 对于配置错误，仍然需要返回错误信息
                raise HTTPException(
                    status_code=500,
                    detail="API token not configured"
                )

            if not task_id:
                # 对于参数错误，仍然需要返回错误信息
                raise HTTPException(
                    status_code=400,
                    detail="Task ID is required"
                )

            headers = self._get_headers()

            # 构建请求URL，将task_id作为查询参数
            url = f"{self.record_info_api_url}?taskId={task_id}"

            logger.info(f"Fetching generation record info for task_id: {task_id}")
            logger.debug(f"Request URL: {url}")

            response = requests.get(
                url,
                headers=headers,
                timeout=30
            )

            response_data = response.json()

            logger.debug(f"API response status: {response.status_code}")
            logger.debug(f"API response data: {response_data}")

            # 🔧 修改：直接返回API响应数据，不进行包装
            # 如果API返回非200状态码，抛出相应的HTTP异常
            if response.status_code != 200:
                error_msg = f"API returned status {response.status_code}"
                if 'msg' in response_data:
                    error_msg += f": {response_data['msg']}"
                logger.error(error_msg)
                raise HTTPException(
                    status_code=response.status_code,
                    detail=error_msg
                )

            logger.info(f"Successfully fetched record info for task_id: {task_id}")

            # 🎯 核心修改：直接返回第三方API的原始响应数据
            return response_data

        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except requests.exceptions.RequestException as e:
            error_msg = f"Request failed: {str(e)}"
            logger.error(error_msg)
            raise HTTPException(
                status_code=500,
                detail=error_msg
            )
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(error_msg)
            raise HTTPException(
                status_code=500,
                detail=error_msg
            )


# 创建服务实例
image_generation_service = ImageGenerationService()


# 示例使用方法
if __name__ == "__main__":
    # 创建请求
    request = ImageGenerationRequest(
        files_url=["https://example.com/image1.png", "https://example.com/image2.png"],
        prompt="A beautiful sunset over the mountains",
        size="1:1",
        callback_url="https://your-callback-url.com/callback",
        is_enhance=False,
        upload_cn=False,
        n_variants=1,
        enable_fallback=False,
        fallback_model="FLUX_MAX"
    )

    # 同步调用
    result = image_generation_service.generate_image_sync(request)
    print(f"Success: {result.success}")
    print(f"Message: {result.message}")
    if result.data:
        print(f"Data: {result.data}")
    if result.error:
        print(f"Error: {result.error}")