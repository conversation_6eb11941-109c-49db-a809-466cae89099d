# /generate-image-sync 接口标准响应格式示例

## 修改说明

已将 `/generate-image-sync` 接口修改为返回标准格式，不再包装在 `detail` 字段中。

## 响应格式

### 成功响应 (HTTP 200)

```json
{
  "code": 0,
  "message": "Image generation successful. Usage recorded against subscription",
  "data": {
    "task_id": "img_gen_123456789",
    "images": [
      {
        "url": "https://generated-image-url.com/image1.jpg",
        "variant": 1,
        "size": "1:1"
      }
    ],
    "remaining_credits": 45,
    "remaining_monthly_usage": 35,
    "subscription_status": "active",
    "generation_record_id": "987fcdeb-51a2-4567-8901-234567890abc",
    "trial_message": null,
    "is_trial_user": false,
    "processing_time_ms": 3500
  }
}
```

### 错误响应示例

#### 权限不足 (HTTP 403)

```json
{
  "code": 1,
  "message": "No active subscription or credits available",
  "data": {
    "remaining_credits": 0,
    "remaining_monthly_usage": 0,
    "subscription_status": "inactive"
  }
}
```

#### 积分不足 (HTTP 403)

```json
{
  "code": 1,
  "message": "Insufficient credits. Need 5, have 2",
  "data": {
    "remaining_credits": 2,
    "credits_needed": 5,
    "subscription_status": "inactive"
  }
}
```

#### 参数错误 (HTTP 422)

```json
{
  "code": 1,
  "message": "Invalid image size specified. Supported sizes: 1:1, 16:9, 9:16, 4:3, 3:4",
  "data": {}
}
```

#### 服务不可用 (HTTP 503)

```json
{
  "code": 1,
  "message": "Image generation service is temporarily unavailable due to configuration issues",
  "data": {}
}
```

#### 内部错误 (HTTP 500)

```json
{
  "code": 1,
  "message": "An unexpected error occurred while processing your request",
  "data": {
    "debug_info": "Connection timeout"
  }
}
```

## 代码值定义

- `code: 0` - 成功
- `code: 1` - 错误（所有类型的错误都使用 1）

## HTTP状态码映射

- `200` - 成功 (code=0)
- `400` - 请求参数错误或API调用失败 (code=1)
- `401` - 用户未登录或token无效 (code=1)
- `403` - 权限不足（无订阅且积分不够、月度次数用完等） (code=1)
- `422` - 请求参数验证失败 (code=1)
- `500` - 服务器内部错误 (code=1)
- `503` - 图片生成服务不可用 (code=1)

## 主要变更

1. **移除外层包装**: 响应不再包装在 `detail` 字段中
2. **统一code值**: 成功使用 0，所有错误使用 1
3. **保持HTTP状态码**: HTTP状态码仍然反映具体的错误类型
4. **标准格式**: 所有响应都包含 `code`, `message`, `data` 三个字段
