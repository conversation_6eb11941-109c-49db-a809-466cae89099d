#!/usr/bin/env python3
"""
测试图片上传功能和数据库记录
"""

import requests
import json
import os
from io import BytesIO

# 测试配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

# 测试用户凭据（需要先登录获取token）
TEST_EMAIL = "<EMAIL>"  # 使用超级用户
TEST_PASSWORD = "changethis"

def get_auth_token():
    """获取认证token"""
    login_data = {
        "username": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(
        f"{API_BASE}/login/access-token",
        data=login_data,
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def create_test_image():
    """创建一个简单的测试图片数据"""
    # 创建一个最小的PNG图片数据（1x1像素的红色PNG）
    # PNG文件头和最小数据
    png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\x0cIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00\x18\xdd\x8d\xb4\x00\x00\x00\x00IEND\xaeB`\x82'
    return png_data

def test_file_upload(token):
    """测试文件上传"""
    print("\n🔍 测试文件上传...")
    
    # 创建测试图片
    image_data = create_test_image()
    
    files = {
        'file': ('test_image.png', image_data, 'image/png')
    }
    
    data = {
        'folder': 'test_uploads',
        'custom_name': 'test_upload.png'
    }
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.post(
        f"{API_BASE}/image/upload",
        files=files,
        data=data,
        headers=headers
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 上传成功!")
        print(f"URL: {result.get('url')}")
        return True
    else:
        print(f"❌ 上传失败: {response.text}")
        return False

def test_url_upload(token):
    """测试URL上传"""
    print("\n🔍 测试URL上传...")
    
    # 使用一个公开的测试图片URL
    test_url = "https://httpbin.org/image/png"
    
    data = {
        "image_url": test_url,
        "file_name": "test_from_url.png",
        "folder": "test_uploads"
    }
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    response = requests.post(
        f"{API_BASE}/image/upload-from-url",
        json=data,
        headers=headers
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ URL上传成功!")
        print(f"URL: {result.get('url')}")
        return True
    else:
        print(f"❌ URL上传失败: {response.text}")
        return False

def check_database_records(token):
    """检查数据库记录（如果有相关API）"""
    print("\n🔍 检查数据库记录...")
    
    # 这里可以添加查询上传记录的API调用
    # 目前只是占位符
    print("📝 数据库记录检查需要实现相应的查询API")

def main():
    """主测试函数"""
    print("🚀 开始测试图片上传功能...")
    
    # 获取认证token
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token，测试终止")
        return
    
    print(f"✅ 成功获取认证token")
    
    # 测试文件上传
    file_upload_success = test_file_upload(token)
    
    # 测试URL上传
    url_upload_success = test_url_upload(token)
    
    # 检查数据库记录
    check_database_records(token)
    
    # 总结
    print("\n📊 测试总结:")
    print(f"文件上传: {'✅ 成功' if file_upload_success else '❌ 失败'}")
    print(f"URL上传: {'✅ 成功' if url_upload_success else '❌ 失败'}")
    
    if file_upload_success and url_upload_success:
        print("🎉 所有测试通过!")
    else:
        print("⚠️ 部分测试失败，请检查日志")

if __name__ == "__main__":
    main()
