#!/usr/bin/env python3
"""
APNS配置检查和测试工具

使用方法:
python check_apns_config.py --check-config
python check_apns_config.py --test-jwt
python check_apns_config.py --test-notification --device-token "your_device_token"
"""

import argparse
import os
import sys
import json
from pathlib import Path

def check_apns_configuration():
    """检查APNS配置"""
    print("🔍 Checking APNS Configuration...")
    print("=" * 50)
    
    # 检查环境变量
    config_items = [
        ("APNS_KEY_ID", "Apple Developer Key ID"),
        ("APNS_TEAM_ID", "Apple Developer Team ID"), 
        ("APNS_BUNDLE_ID", "App Bundle ID"),
        ("APNS_KEY_PATH", "Path to .p8 key file"),
        ("APNS_USE_SANDBOX", "Use sandbox environment")
    ]
    
    missing_configs = []
    
    for env_var, description in config_items:
        value = os.getenv(env_var, "")
        status = "✅" if value else "❌"
        print(f"{status} {env_var}: {value or 'NOT SET'}")
        print(f"   Description: {description}")
        
        if not value:
            missing_configs.append(env_var)
        print()
    
    # 检查.p8密钥文件
    key_path = os.getenv("APNS_KEY_PATH", "")
    if key_path:
        if os.path.exists(key_path):
            print(f"✅ Key file exists: {key_path}")
            try:
                with open(key_path, 'r') as f:
                    content = f.read()
                    if "BEGIN PRIVATE KEY" in content and "END PRIVATE KEY" in content:
                        print("✅ Key file format appears valid")
                    else:
                        print("❌ Key file format may be invalid")
                        missing_configs.append("APNS_KEY_FILE_FORMAT")
            except Exception as e:
                print(f"❌ Cannot read key file: {e}")
                missing_configs.append("APNS_KEY_FILE_READ")
        else:
            print(f"❌ Key file not found: {key_path}")
            missing_configs.append("APNS_KEY_FILE_EXISTS")
    
    print("\n" + "=" * 50)
    if missing_configs:
        print("❌ APNS Configuration Issues Found:")
        for config in missing_configs:
            print(f"   - {config}")
        print("\n💡 To fix these issues:")
        print("1. Set the missing environment variables")
        print("2. Download your .p8 key file from Apple Developer Console")
        print("3. Place the key file in a secure location")
        print("4. Update APNS_KEY_PATH to point to the key file")
        return False
    else:
        print("✅ APNS Configuration looks good!")
        return True


def test_jwt_generation():
    """测试JWT生成"""
    print("🔐 Testing JWT Generation...")
    print("=" * 50)
    
    try:
        from app.services.apns_service import APNSService
        
        apns_service = APNSService()
        
        if not apns_service.is_configured():
            print("❌ APNS not configured. Please run --check-config first.")
            return False
        
        print("📝 Generating JWT token...")
        token = apns_service._generate_jwt_token()
        
        if token:
            print(f"✅ JWT token generated successfully!")
            print(f"   Token length: {len(token)} characters")
            print(f"   Token preview: {token[:50]}...")
            return True
        else:
            print("❌ Failed to generate JWT token")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running this from the backend directory")
        return False
    except Exception as e:
        print(f"❌ JWT generation failed: {e}")
        return False


def test_apns_notification(device_token: str):
    """测试APNS通知发送"""
    print("📱 Testing APNS Notification...")
    print("=" * 50)
    
    try:
        import asyncio
        from app.services.apns_service import APNSService
        
        apns_service = APNSService()
        
        if not apns_service.is_configured():
            print("❌ APNS not configured. Please run --check-config first.")
            return False
        
        print(f"📤 Sending test notification to device: {device_token[:10]}...")
        
        async def send_test():
            return await apns_service.send_notification(
                device_token=device_token,
                title="APNS Test",
                body="This is a test notification from your app",
                data={"test": True}
            )
        
        success = asyncio.run(send_test())
        
        if success:
            print("✅ Test notification sent successfully!")
            print("📱 Check your device for the notification")
            return True
        else:
            print("❌ Failed to send test notification")
            print("💡 Check the application logs for detailed error information")
            return False
            
    except Exception as e:
        print(f"❌ Notification test failed: {e}")
        return False


def create_sample_env_file():
    """创建示例环境配置文件"""
    print("📝 Creating sample .env configuration...")
    
    sample_config = """
# Apple Push Notification Service (APNS) Configuration
# Get these values from your Apple Developer Account

# Your Apple Developer Key ID (10 characters)
APNS_KEY_ID=ABC1234567

# Your Apple Developer Team ID (10 characters) 
APNS_TEAM_ID=DEF7890123

# Your app's Bundle ID
APNS_BUNDLE_ID=com.yourcompany.yourapp

# Path to your .p8 private key file
APNS_KEY_PATH=/path/to/your/AuthKey_ABC1234567.p8

# Use sandbox environment for development (true) or production (false)
APNS_USE_SANDBOX=true
"""
    
    sample_file = "apns_config_sample.env"
    with open(sample_file, 'w') as f:
        f.write(sample_config.strip())
    
    print(f"✅ Sample configuration created: {sample_file}")
    print("💡 Copy these settings to your .env file and update with your actual values")


def main():
    parser = argparse.ArgumentParser(description="APNS配置检查和测试工具")
    parser.add_argument("--check-config", action="store_true", help="检查APNS配置")
    parser.add_argument("--test-jwt", action="store_true", help="测试JWT生成")
    parser.add_argument("--test-notification", action="store_true", help="测试推送通知")
    parser.add_argument("--device-token", help="设备令牌（用于测试通知）")
    parser.add_argument("--create-sample", action="store_true", help="创建示例配置文件")
    
    args = parser.parse_args()
    
    if not any([args.check_config, args.test_jwt, args.test_notification, args.create_sample]):
        parser.print_help()
        return
    
    print("🍎 APNS Configuration and Test Tool")
    print("=" * 60)
    
    success = True
    
    if args.create_sample:
        create_sample_env_file()
        print()
    
    if args.check_config:
        success &= check_apns_configuration()
        print()
    
    if args.test_jwt:
        success &= test_jwt_generation()
        print()
    
    if args.test_notification:
        if not args.device_token:
            print("❌ Device token is required for notification testing")
            print("Use: --test-notification --device-token YOUR_DEVICE_TOKEN")
            success = False
        else:
            success &= test_apns_notification(args.device_token)
        print()
    
    print("=" * 60)
    if success:
        print("🎉 All tests passed!")
    else:
        print("💥 Some tests failed. Please check the issues above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
