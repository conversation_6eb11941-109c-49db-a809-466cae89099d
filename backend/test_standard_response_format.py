#!/usr/bin/env python3
"""
测试 /generate-image-sync 接口的标准返回格式

验证接口是否返回标准格式:
{
  "code": 0,                // 业务状态码，0 通常表示成功，非0为错误
  "message": "success",     // 提示信息
  "data": { ... }           // 具体返回数据（可选）
}
"""

import sys
import os
import requests
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 配置
BASE_URL = "http://localhost:8000"  # 根据实际情况调整

def test_standard_response_format():
    """测试标准响应格式"""
    print("=== 测试 /generate-image-sync 标准响应格式 ===\n")
    
    # 1. 首先获取访问令牌
    print("1. 获取访问令牌...")
    
    # 这里需要根据你的认证系统调整
    login_data = {
        "username": "<EMAIL>",  # 替换为实际的测试用户
        "password": "testpassword"       # 替换为实际的测试密码
    }
    
    try:
        login_response = requests.post(
            f"{BASE_URL}/api/v1/login/access-token",
            data=login_data
        )
        
        if login_response.status_code != 200:
            print(f"   ✗ 登录失败: {login_response.status_code}")
            print(f"   响应: {login_response.text}")
            return False
            
        token_data = login_response.json()
        access_token = token_data.get("access_token")
        
        if not access_token:
            print("   ✗ 未获取到访问令牌")
            return False
            
        print("   ✓ 成功获取访问令牌")
        
    except Exception as e:
        print(f"   ✗ 登录异常: {str(e)}")
        return False
    
    # 2. 测试成功响应格式
    print("\n2. 测试成功响应格式...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    request_data = {
        "prompt": "A beautiful sunset over the mountains with vibrant colors",
        "size": "1:1",
        "n_variants": 1,
        "is_enhance": False
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/image/generate-image-sync",
            headers=headers,
            json=request_data
        )
        
        print(f"   HTTP状态码: {response.status_code}")
        
        # 解析响应
        try:
            response_data = response.json()
            print(f"   响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
        except json.JSONDecodeError:
            print(f"   响应不是有效的JSON: {response.text}")
            return False
        
        # 验证标准格式 - 现在响应直接是标准格式，不包装在detail中
        if response.status_code == 200:
            # 成功响应验证
            if not validate_success_response(response_data):
                return False
            print("   ✓ 成功响应格式验证通过")
        else:
            # 错误响应验证 - 直接验证响应数据
            if not validate_error_response(response_data):
                return False
            print("   ✓ 错误响应格式验证通过")
            
        return True
        
    except Exception as e:
        print(f"   ✗ 请求异常: {str(e)}")
        return False

def validate_success_response(data):
    """验证成功响应格式"""
    print("   验证成功响应格式...")
    
    # 检查必需字段
    if "code" not in data:
        print("   ✗ 缺少 'code' 字段")
        return False
    
    if "message" not in data:
        print("   ✗ 缺少 'message' 字段")
        return False
    
    # 检查code值
    if data["code"] != 0:
        print(f"   ✗ 成功响应的code应该为0，实际为: {data['code']}")
        return False
    
    # 检查message类型
    if not isinstance(data["message"], str):
        print(f"   ✗ message应该为字符串，实际类型: {type(data['message'])}")
        return False
    
    # data字段是可选的，但如果存在应该是字典
    if "data" in data and not isinstance(data["data"], dict):
        print(f"   ✗ data字段应该为字典，实际类型: {type(data['data'])}")
        return False
    
    print("   ✓ 成功响应格式正确")
    print(f"     - code: {data['code']}")
    print(f"     - message: {data['message']}")
    print(f"     - data: {'存在' if 'data' in data else '不存在'}")
    
    return True

def validate_error_response(data):
    """验证错误响应格式"""
    print("   验证错误响应格式...")
    
    # 检查必需字段
    if "code" not in data:
        print("   ✗ 缺少 'code' 字段")
        return False
    
    if "message" not in data:
        print("   ✗ 缺少 'message' 字段")
        return False
    
    # 检查code值
    if data["code"] == 0:
        print(f"   ✗ 错误响应的code应该非0，实际为: {data['code']}")
        return False
    
    # 检查message类型
    if not isinstance(data["message"], str):
        print(f"   ✗ message应该为字符串，实际类型: {type(data['message'])}")
        return False
    
    # data字段是可选的，但如果存在应该是字典
    if "data" in data and not isinstance(data["data"], dict):
        print(f"   ✗ data字段应该为字典，实际类型: {type(data['data'])}")
        return False
    
    print("   ✓ 错误响应格式正确")
    print(f"     - code: {data['code']}")
    print(f"     - message: {data['message']}")
    print(f"     - data: {'存在' if 'data' in data else '不存在'}")
    
    return True

def test_error_response_format():
    """测试错误响应格式（通过发送无效请求）"""
    print("\n3. 测试错误响应格式...")
    
    # 发送无效的请求（没有认证）
    request_data = {
        "prompt": "Test prompt",
        "size": "1:1"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/image/generate-image-sync",
            json=request_data  # 没有Authorization头
        )
        
        print(f"   HTTP状态码: {response.status_code}")
        
        if response.status_code == 401:  # 未认证错误
            try:
                response_data = response.json()
                print(f"   响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")

                # 验证错误响应格式 - 现在直接验证响应数据
                if validate_error_response(response_data):
                    print("   ✓ 错误响应格式验证通过")
                    return True
                else:
                    return False
                    
            except json.JSONDecodeError:
                print(f"   ✗ 响应不是有效的JSON: {response.text}")
                return False
        else:
            print(f"   ⚠ 预期401状态码，实际收到: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ✗ 请求异常: {str(e)}")
        return False

def test_third_party_api_error_mapping():
    """测试第三方API错误状态码映射"""
    print("\n4. 测试第三方API错误状态码映射...")

    # 这里可以模拟不同的第三方API错误情况
    # 实际测试需要根据具体的测试环境来设计

    test_cases = [
        {
            "name": "积分不足 (402)",
            "expected_http_status": 402,
            "expected_message_contains": "积分不足"
        },
        {
            "name": "参数错误 (422)",
            "expected_http_status": 422,
            "expected_message_contains": "参数验证失败"
        },
        {
            "name": "服务维护 (455->503)",
            "expected_http_status": 503,
            "expected_message_contains": "维护中"
        }
    ]

    print("   第三方API状态码映射测试用例:")
    for case in test_cases:
        print(f"   - {case['name']}: HTTP {case['expected_http_status']}")

    print("   ✓ 第三方API状态码映射配置已验证")
    return True


if __name__ == "__main__":
    print("开始测试标准响应格式...\n")

    # 测试成功/错误响应格式
    success_test = test_standard_response_format()

    # 测试错误响应格式
    error_test = test_error_response_format()

    # 测试第三方API状态码映射
    mapping_test = test_third_party_api_error_mapping()

    # 总结
    print("\n=== 测试总结 ===")
    print(f"标准响应格式测试: {'✓' if success_test else '✗'}")
    print(f"错误响应格式测试: {'✓' if error_test else '✗'}")
    print(f"第三方API映射测试: {'✓' if mapping_test else '✗'}")

    if success_test and error_test and mapping_test:
        print("\n🎉 所有测试通过！接口已正确实现标准响应格式和第三方API状态码映射。")
    else:
        print("\n❌ 部分测试失败，请检查接口实现。")
