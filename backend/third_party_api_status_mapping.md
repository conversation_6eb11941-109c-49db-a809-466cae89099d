# 第三方图片生成API状态码映射

## 第三方API状态码说明

| 第三方状态码 | 含义 | 映射到HTTP状态码 | 标准响应格式 |
|-------------|------|----------------|-------------|
| 200 | 成功 - 请求已成功处理 | 200 | `{"code": 0, "message": "success", "data": {...}}` |
| 400 | 格式错误 - 参数不是有效的 JSON 格式 | 400 | `{"code": 1, "message": "图片生成请求格式错误，请检查参数格式", "data": {...}}` |
| 401 | 未授权 - 缺少身份验证凭据或凭据无效 | 401 | `{"code": 1, "message": "图片生成服务认证失败，请稍后重试", "data": {...}}` |
| 402 | 积分不足 - 账户没有足够的积分执行此操作 | 402 | `{"code": 1, "message": "图片生成积分不足，请充值后重试", "data": {...}}` |
| 404 | 未找到 - 请求的资源或端点不存在 | 404 | `{"code": 1, "message": "图片生成服务端点不存在", "data": {...}}` |
| 422 | 参数错误 - 请求参数未通过验证检查 | 422 | `{"code": 1, "message": "图片生成参数验证失败，请检查输入参数", "data": {...}}` |
| 429 | 超出限制 - 已超过对此资源的请求限制 | 429 | `{"code": 1, "message": "图片生成请求过于频繁，请稍后重试", "data": {...}}` |
| 455 | 服务不可用 - 系统当前正在进行维护 | 503 | `{"code": 1, "message": "图片生成服务正在维护中，请稍后重试", "data": {...}}` |
| 500 | 服务器错误 - 在处理请求时发生意外错误 | 500 | `{"code": 1, "message": "图片生成服务内部错误，请稍后重试", "data": {...}}` |
| 550 | 连接被拒绝 - 任务因队列已满而被拒绝 | 503 | `{"code": 1, "message": "图片生成服务队列已满，请稍后重试", "data": {...}}` |

## 响应示例

### 成功响应 (第三方API返回200)

**第三方API响应:**
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "task_id": "img_gen_123456789",
    "images": [
      {
        "url": "https://generated-image-url.com/image1.jpg",
        "variant": 1,
        "size": "1:1"
      }
    ]
  }
}
```

**我们的标准响应 (HTTP 200):**
```json
{
  "code": 0,
  "message": "Image generation request submitted successfully",
  "data": {
    "task_id": "img_gen_123456789",
    "images": [
      {
        "url": "https://generated-image-url.com/image1.jpg",
        "variant": 1,
        "size": "1:1"
      }
    ],
    "remaining_credits": 45,
    "remaining_monthly_usage": 35,
    "subscription_status": "active",
    "generation_record_id": "987fcdeb-51a2-4567-8901-234567890abc"
  }
}
```

### 错误响应示例

#### 积分不足 (第三方API返回402)

**第三方API响应:**
```json
{
  "code": 402,
  "msg": "Insufficient credits"
}
```

**我们的标准响应 (HTTP 402):**
```json
{
  "code": 1,
  "message": "图片生成积分不足，请充值后重试: Insufficient credits",
  "data": {
    "code": 402,
    "msg": "Insufficient credits"
  }
}
```

#### 参数错误 (第三方API返回422)

**第三方API响应:**
```json
{
  "code": 422,
  "msg": "Invalid prompt format"
}
```

**我们的标准响应 (HTTP 422):**
```json
{
  "code": 1,
  "message": "图片生成参数验证失败，请检查输入参数: Invalid prompt format",
  "data": {
    "code": 422,
    "msg": "Invalid prompt format"
  }
}
```

#### 服务维护 (第三方API返回455)

**第三方API响应:**
```json
{
  "code": 455,
  "msg": "Service under maintenance"
}
```

**我们的标准响应 (HTTP 503):**
```json
{
  "code": 1,
  "message": "图片生成服务正在维护中，请稍后重试: Service under maintenance",
  "data": {
    "code": 455,
    "msg": "Service under maintenance"
  }
}
```

## 实现细节

1. **状态码映射**: 在 `app/models/image_generation_responses.py` 中定义了 `THIRD_PARTY_API_STATUS_MAPPING` 和 `get_third_party_error_info()` 函数

2. **错误处理**: 在 `app/services/generation_image.py` 中的 `generate_image_sync()` 和 `generate_image()` 方法中处理第三方API的错误响应

3. **标准化响应**: 在 `app/api/routes/image_generation.py` 中将所有响应转换为标准格式

4. **错误代码**: 第三方API错误使用 `API_ERROR_{status_code}` 格式的错误代码，例如 `API_ERROR_402`

## 注意事项

- 所有第三方API错误都会映射到我们的标准响应格式
- HTTP状态码保持与第三方API的语义一致
- 原始的第三方API响应数据会保存在 `data` 字段中供调试使用
- 错误消息会结合我们的中文提示和第三方API的原始消息
