#!/usr/bin/env python3
"""
APNS配置和测试设置工具

这个脚本帮助你：
1. 配置APNS环境变量
2. 添加真实的设备令牌
3. 测试APNS推送通知
4. 验证完整的推送流程

使用方法:
python setup_apns_test.py --setup-config
python setup_apns_test.py --add-device-token "your_real_device_token" --user-id "user_uuid"
python setup_apns_test.py --test-notification --user-id "user_uuid"
python setup_apns_test.py --full-test
"""

import argparse
import os
import sys
import uuid
import json
import asyncio
from pathlib import Path

def setup_apns_config():
    """设置APNS配置"""
    print("🍎 APNS Configuration Setup")
    print("=" * 60)
    
    print("📋 To enable APNS push notifications, you need:")
    print("1. Apple Developer Account")
    print("2. APNs Auth Key (.p8 file)")
    print("3. App Bundle ID")
    print("4. Team ID and Key ID")
    print()
    
    # 检查当前配置
    config_items = [
        ("APNS_KEY_ID", "Apple Developer Key ID (10 characters)"),
        ("APNS_TEAM_ID", "Apple Developer Team ID (10 characters)"), 
        ("APNS_BUNDLE_ID", "App Bundle ID (e.g., com.yourcompany.yourapp)"),
        ("APNS_KEY_PATH", "Path to .p8 key file"),
        ("APNS_USE_SANDBOX", "Use sandbox environment (true/false)")
    ]
    
    print("📊 Current Configuration:")
    for env_var, description in config_items:
        value = os.getenv(env_var, "")
        status = "✅" if value else "❌"
        print(f"{status} {env_var}: {value or 'NOT SET'}")
    print()
    
    # 提供配置指导
    print("🔧 To configure APNS:")
    print("1. Go to https://developer.apple.com/account/")
    print("2. Navigate to 'Certificates, Identifiers & Profiles'")
    print("3. Create an APNs Auth Key (.p8 file)")
    print("4. Download the .p8 file and note the Key ID")
    print("5. Find your Team ID in your developer account")
    print("6. Set the environment variables:")
    print()
    print("   export APNS_KEY_ID='YOUR_KEY_ID'")
    print("   export APNS_TEAM_ID='YOUR_TEAM_ID'")
    print("   export APNS_BUNDLE_ID='com.yourcompany.yourapp'")
    print("   export APNS_KEY_PATH='/path/to/AuthKey_KEYID.p8'")
    print("   export APNS_USE_SANDBOX='true'  # for development")
    print()
    
    # 创建示例.env文件
    env_file = Path(".env.apns.example")
    with open(env_file, 'w') as f:
        f.write("""# APNS Configuration Example
# Copy these to your .env file and update with your actual values

APNS_KEY_ID=ABC1234567
APNS_TEAM_ID=DEF7890123
APNS_BUNDLE_ID=com.yourcompany.yourapp
APNS_KEY_PATH=/path/to/AuthKey_ABC1234567.p8
APNS_USE_SANDBOX=true
""")
    
    print(f"📝 Created example configuration file: {env_file}")
    print("💡 Copy these settings to your .env file and update with your actual values")

def add_real_device_token(user_id: str, device_token: str):
    """添加真实的设备令牌"""
    print("📱 Adding Real Device Token")
    print("=" * 60)
    
    # 验证设备令牌格式
    if len(device_token) != 64:
        print(f"❌ Invalid device token length: {len(device_token)} (expected 64)")
        return False
    
    if not all(c in '0123456789abcdefABCDEF' for c in device_token):
        print("❌ Invalid device token format: must be hexadecimal")
        return False
    
    print(f"✅ Device token format is valid")
    print(f"📋 User ID: {user_id}")
    print(f"📋 Device Token: {device_token[:10]}...{device_token[-10:]}")
    print()
    
    try:
        from app.core.db import engine
        from sqlalchemy import text
        
        # 删除旧的测试令牌
        with engine.connect() as conn:
            # 删除旧的无效令牌
            delete_result = conn.execute(text('''
                DELETE FROM devicetoken 
                WHERE user_id = :user_id 
                AND (LENGTH(device_token) != 64 OR device_token LIKE '%test%' OR device_token LIKE '%user_%')
            '''), {'user_id': user_id})
            
            deleted_count = delete_result.rowcount
            if deleted_count > 0:
                print(f"🗑️  Removed {deleted_count} invalid test tokens")
            
            # 检查是否已存在相同的令牌
            check_result = conn.execute(text('''
                SELECT id FROM devicetoken 
                WHERE user_id = :user_id AND device_token = :device_token
            '''), {'user_id': user_id, 'device_token': device_token})
            
            existing = check_result.fetchone()
            
            if existing:
                print("⚠️  Device token already exists, updating...")
                conn.execute(text('''
                    UPDATE devicetoken 
                    SET is_active = true, updated_at = NOW()
                    WHERE user_id = :user_id AND device_token = :device_token
                '''), {'user_id': user_id, 'device_token': device_token})
            else:
                # 插入新的设备令牌
                token_id = str(uuid.uuid4())
                conn.execute(text('''
                    INSERT INTO devicetoken (id, user_id, device_token, platform, is_active, created_at, updated_at)
                    VALUES (:token_id, :user_id, :device_token, 'ios', true, NOW(), NOW())
                '''), {
                    'token_id': token_id,
                    'user_id': user_id,
                    'device_token': device_token
                })
                print(f"✅ Added new device token: {token_id}")
            
            conn.commit()
            
            # 验证插入
            verify_result = conn.execute(text('''
                SELECT id, device_token, platform, is_active, created_at
                FROM devicetoken 
                WHERE user_id = :user_id AND device_token = :device_token
            '''), {'user_id': user_id, 'device_token': device_token})
            
            token_record = verify_result.fetchone()
            
            if token_record:
                print("🔍 Verification:")
                print(f"   Token ID: {token_record[0]}")
                print(f"   Device Token: {token_record[1][:10]}...{token_record[1][-10:]}")
                print(f"   Platform: {token_record[2]}")
                print(f"   Is Active: {token_record[3]}")
                print(f"   Created At: {token_record[4]}")
                return True
            else:
                print("❌ Failed to verify token insertion")
                return False
                
    except Exception as e:
        print(f"❌ Error adding device token: {e}")
        return False

async def test_apns_notification(user_id: str):
    """测试APNS推送通知"""
    print("🧪 Testing APNS Notification")
    print("=" * 60)
    
    try:
        from app.services.apns_service import APNSService
        from app.core.db import engine
        from sqlmodel import Session
        
        # 检查APNS配置
        with Session(engine) as session:
            apns_service = APNSService(session)
            
            if not apns_service.is_configured():
                print("❌ APNS is not configured")
                print("💡 Run: python setup_apns_test.py --setup-config")
                return False
            
            print("✅ APNS is configured")
            print(f"📋 Key ID: {apns_service.key_id}")
            print(f"📋 Team ID: {apns_service.team_id}")
            print(f"📋 Bundle ID: {apns_service.bundle_id}")
            print(f"📋 Use Sandbox: {apns_service.use_sandbox}")
            print()
            
            # 发送测试通知
            print("📤 Sending test notification...")
            result = await apns_service.send_to_user(
                user_id=uuid.UUID(user_id),
                title="APNS Test",
                body="This is a test notification from your backend",
                data={"test": True, "timestamp": str(uuid.uuid4())}
            )
            
            print("📊 Notification Result:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            if result.get("success"):
                successful = result.get("successful_devices", 0)
                total = result.get("total_devices", 0)
                
                if successful > 0:
                    print(f"🎉 SUCCESS: Sent to {successful}/{total} devices!")
                    print("📱 Check your iOS device for the notification")
                    return True
                else:
                    print(f"⚠️  No successful sends ({successful}/{total})")
                    print("💡 Check device tokens and APNS configuration")
                    return False
            else:
                print("❌ Notification sending failed")
                print(f"   Reason: {result.get('skipped_reason', 'Unknown error')}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing notification: {e}")
        return False

def full_test_flow():
    """完整的测试流程"""
    print("🧪 FULL APNS TEST FLOW")
    print("=" * 80)
    
    print("This test will:")
    print("1. Check APNS configuration")
    print("2. Check device tokens")
    print("3. Send test notification")
    print("4. Verify the complete flow")
    print()
    
    # 这里可以添加完整的测试流程
    print("💡 To run the full test:")
    print("1. First setup APNS config: python setup_apns_test.py --setup-config")
    print("2. Add real device token: python setup_apns_test.py --add-device-token YOUR_TOKEN --user-id USER_ID")
    print("3. Test notification: python setup_apns_test.py --test-notification --user-id USER_ID")

def main():
    parser = argparse.ArgumentParser(description="APNS配置和测试工具")
    parser.add_argument("--setup-config", action="store_true", help="设置APNS配置")
    parser.add_argument("--add-device-token", help="添加真实的设备令牌")
    parser.add_argument("--user-id", help="用户ID")
    parser.add_argument("--test-notification", action="store_true", help="测试推送通知")
    parser.add_argument("--full-test", action="store_true", help="运行完整测试流程")
    
    args = parser.parse_args()
    
    if not any([args.setup_config, args.add_device_token, args.test_notification, args.full_test]):
        parser.print_help()
        return
    
    if args.setup_config:
        setup_apns_config()
    
    if args.add_device_token:
        if not args.user_id:
            print("❌ --user-id is required when adding device token")
            sys.exit(1)
        
        success = add_real_device_token(args.user_id, args.add_device_token)
        if not success:
            sys.exit(1)
    
    if args.test_notification:
        if not args.user_id:
            print("❌ --user-id is required for testing notification")
            sys.exit(1)
        
        success = asyncio.run(test_apns_notification(args.user_id))
        if not success:
            sys.exit(1)
    
    if args.full_test:
        full_test_flow()

if __name__ == "__main__":
    main()
